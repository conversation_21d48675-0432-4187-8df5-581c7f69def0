import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Category } from '@/types';

interface CategoryCardProps {
  category: Category;
  className?: string;
}

const CategoryCard: React.FC<CategoryCardProps> = ({ category, className = '' }) => {
  return (
    <Link href={`/category/${category.slug}`}>
      <div className={`relative group cursor-pointer overflow-hidden rounded-lg ${className}`}>
        <div className="relative h-48 w-full">
          <Image
            src={category.image}
            alt={category.name}
            fill
            className="object-cover transition-transform duration-300 group-hover:scale-105"
          />
          <div className="absolute inset-0 bg-black/40 group-hover:bg-black/50 transition-colors duration-300" />
        </div>
        
        <div className="absolute inset-0 flex flex-col justify-end p-4">
          <h3 className="text-white text-xl font-bold mb-1 group-hover:text-orange-300 transition-colors duration-300">
            {category.name}
          </h3>
          <p className="text-white/90 text-sm mb-2">
            {category.description}
          </p>
          <p className="text-orange-300 text-sm font-medium">
            {category.recipeCount.toLocaleString()} recipes
          </p>
        </div>
      </div>
    </Link>
  );
};

export default CategoryCard;

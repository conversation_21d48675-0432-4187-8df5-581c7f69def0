import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Clock, Users, Star, Heart } from 'lucide-react';
import { Recipe } from '@/types';

interface RecipeCardProps {
  recipe: Recipe;
  className?: string;
}

const RecipeCard: React.FC<RecipeCardProps> = ({ recipe, className = '' }) => {
  return (
    <div className={`bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300 ${className}`}>
      <Link href={`/recipe/${recipe.id}`}>
        <div className="relative">
          <Image
            src={recipe.image}
            alt={recipe.title}
            width={400}
            height={250}
            className="w-full h-48 object-cover"
          />
          <button className="absolute top-3 right-3 p-2 bg-white/80 rounded-full hover:bg-white transition-colors duration-200">
            <Heart className="h-5 w-5 text-gray-600 hover:text-red-500" />
          </button>
          <div className="absolute bottom-3 left-3 bg-black/70 text-white px-2 py-1 rounded text-sm">
            {recipe.difficulty}
          </div>
        </div>
      </Link>

      <div className="p-4">
        <Link href={`/recipe/${recipe.id}`}>
          <h3 className="text-lg font-semibold text-gray-900 mb-2 hover:text-orange-600 transition-colors duration-200 line-clamp-2">
            {recipe.title}
          </h3>
        </Link>
        
        <p className="text-gray-600 text-sm mb-3 line-clamp-2">
          {recipe.description}
        </p>

        <div className="flex items-center justify-between text-sm text-gray-500 mb-3">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-1">
              <Clock className="h-4 w-4" />
              <span>{recipe.totalTime}m</span>
            </div>
            <div className="flex items-center space-x-1">
              <Users className="h-4 w-4" />
              <span>{recipe.servings}</span>
            </div>
          </div>
          
          <div className="flex items-center space-x-1">
            <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
            <span className="font-medium">{recipe.rating}</span>
            <span className="text-gray-400">({recipe.reviewCount})</span>
          </div>
        </div>

        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Image
              src={recipe.author.avatar || 'https://picsum.photos/100/100?random=50'}
              alt={recipe.author.name}
              width={24}
              height={24}
              className="w-6 h-6 rounded-full"
            />
            <span className="text-sm text-gray-600">{recipe.author.name}</span>
          </div>
          
          <div className="flex flex-wrap gap-1">
            {recipe.tags.slice(0, 2).map((tag) => (
              <span
                key={tag}
                className="px-2 py-1 bg-orange-100 text-orange-700 text-xs rounded-full"
              >
                {tag}
              </span>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default RecipeCard;

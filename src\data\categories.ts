import { Category } from '@/types';

export const categories: Category[] = [
  {
    id: '1',
    name: 'Dinners',
    slug: 'dinners',
    description: 'Delicious dinner recipes for every occasion',
    image: 'https://picsum.photos/400/250?random=20',
    recipeCount: 1250
  },
  {
    id: '2',
    name: 'Breakfast & Brunch',
    slug: 'breakfast-brunch',
    description: 'Start your day with amazing breakfast recipes',
    image: 'https://picsum.photos/400/250?random=21',
    recipeCount: 850
  },
  {
    id: '3',
    name: 'Lunch',
    slug: 'lunch',
    description: 'Quick and satisfying lunch ideas',
    image: 'https://picsum.photos/400/250?random=22',
    recipeCount: 650
  },
  {
    id: '4',
    name: 'Appetizers & Snacks',
    slug: 'appetizers-snacks',
    description: 'Perfect starters and snacks for any gathering',
    image: 'https://picsum.photos/400/250?random=23',
    recipeCount: 750
  },
  {
    id: '5',
    name: '<PERSON><PERSON><PERSON>',
    slug: 'desserts',
    description: 'Sweet treats and desserts to satisfy your cravings',
    image: 'https://picsum.photos/400/250?random=24',
    recipeCount: 950
  },
  {
    id: '6',
    name: 'Sal<PERSON>',
    slug: 'salads',
    description: 'Fresh and healthy salad recipes',
    image: 'https://picsum.photos/400/250?random=25',
    recipeCount: 450
  },
  {
    id: '7',
    name: 'Soups & Stews',
    slug: 'soups-stews',
    description: 'Comforting soups and hearty stews',
    image: 'https://picsum.photos/400/250?random=26',
    recipeCount: 550
  },
  {
    id: '8',
    name: 'Side Dishes',
    slug: 'side-dishes',
    description: 'Perfect accompaniments to your main dishes',
    image: 'https://picsum.photos/400/250?random=27',
    recipeCount: 400
  },
  {
    id: '9',
    name: 'Drinks',
    slug: 'drinks',
    description: 'Refreshing beverages and cocktails',
    image: 'https://picsum.photos/400/250?random=28',
    recipeCount: 300
  },
  {
    id: '10',
    name: 'Bread & Baking',
    slug: 'bread-baking',
    description: 'Homemade breads and baked goods',
    image: 'https://picsum.photos/400/250?random=29',
    recipeCount: 500
  }
];

export const cuisines = [
  { id: '1', name: 'Italian', slug: 'italian' },
  { id: '2', name: 'Mexican', slug: 'mexican' },
  { id: '3', name: 'Chinese', slug: 'chinese' },
  { id: '4', name: 'Indian', slug: 'indian' },
  { id: '5', name: 'French', slug: 'french' },
  { id: '6', name: 'Japanese', slug: 'japanese' },
  { id: '7', name: 'Thai', slug: 'thai' },
  { id: '8', name: 'Greek', slug: 'greek' },
  { id: '9', name: 'American', slug: 'american' },
  { id: '10', name: 'Mediterranean', slug: 'mediterranean' }
];

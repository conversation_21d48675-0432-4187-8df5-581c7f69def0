'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { Search, Menu, X, User, Heart, Plus } from 'lucide-react';

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [isScrolled, setIsScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 0);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  useEffect(() => {
    if (isSidebarOpen) {
      document.body.classList.add('sidebar-open');
    } else {
      document.body.classList.remove('sidebar-open');
    }

    return () => {
      document.body.classList.remove('sidebar-open');
    };
  }, [isSidebarOpen]);

  const navigationItems = [
    {
      name: 'Dinners',
      href: '/dinners',
      submenu: [
        { name: '5-Ingredient Dinners', href: '/dinners/5-ingredient' },
        { name: 'One-Pot Meals', href: '/dinners/one-pot' },
        { name: 'Quick & Easy', href: '/dinners/quick-easy' },
        { name: '30-Minute Meals', href: '/dinners/30-minute' },
        { name: 'Family Dinners', href: '/dinners/family' },
        { name: 'Comfort Food', href: '/dinners/comfort-food' }
      ]
    },
    {
      name: 'Meals',
      href: '/meals',
      submenu: [
        { name: 'Breakfast & Brunch', href: '/meals/breakfast-brunch' },
        { name: 'Lunch', href: '/meals/lunch' },
        { name: 'Healthy', href: '/meals/healthy' },
        { name: 'Appetizers & Snacks', href: '/meals/appetizers-snacks' },
        { name: 'Salads', href: '/meals/salads' },
        { name: 'Side Dishes', href: '/meals/side-dishes' },
        { name: 'Soups', href: '/meals/soups' },
        { name: 'Desserts', href: '/meals/desserts' }
      ]
    },
    {
      name: 'Ingredients',
      href: '/ingredients',
      submenu: [
        { name: 'Chicken', href: '/ingredients/chicken' },
        { name: 'Beef', href: '/ingredients/beef' },
        { name: 'Pork', href: '/ingredients/pork' },
        { name: 'Seafood', href: '/ingredients/seafood' },
        { name: 'Pasta', href: '/ingredients/pasta' },
        { name: 'Vegetables', href: '/ingredients/vegetables' }
      ]
    },
    {
      name: 'Occasions',
      href: '/occasions',
      submenu: [
        { name: 'Summer Recipes', href: '/occasions/summer' },
        { name: 'Holiday Recipes', href: '/occasions/holidays' },
        { name: 'Party Food', href: '/occasions/party' },
        { name: 'Quick Weeknight', href: '/occasions/weeknight' }
      ]
    },
    {
      name: 'Cuisines',
      href: '/cuisines',
      submenu: [
        { name: 'Italian', href: '/cuisines/italian' },
        { name: 'Mexican', href: '/cuisines/mexican' },
        { name: 'Chinese', href: '/cuisines/chinese' },
        { name: 'Indian', href: '/cuisines/indian' },
        { name: 'French', href: '/cuisines/french' },
        { name: 'Japanese', href: '/cuisines/japanese' }
      ]
    },
    {
      name: 'Kitchen Tips',
      href: '/kitchen-tips',
      submenu: [
        { name: 'Cooking Techniques', href: '/kitchen-tips/techniques' },
        { name: 'Equipment Reviews', href: '/kitchen-tips/equipment' },
        { name: 'Ingredient Substitutions', href: '/kitchen-tips/substitutions' },
        { name: 'Meal Planning', href: '/kitchen-tips/meal-planning' }
      ]
    }
  ];

  return (
    <>
      {/* Top Bar - Hidden when scrolled */}
      <div className={`bg-orange-500 text-white py-2 transition-all duration-300 ${isScrolled ? 'h-0 overflow-hidden py-0' : 'h-auto'}`}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center text-sm">
            <div className="flex space-x-4">
              <Link href="/newsletter" className="hover:underline">Newsletter</Link>
              <Link href="/magazine" className="hover:underline">Magazine</Link>
            </div>
            <div className="flex space-x-4">
              <Link href="/login" className="hover:underline">Log In</Link>
              <Link href="/signup" className="hover:underline">Sign Up</Link>
            </div>
          </div>
        </div>
      </div>

      {/* Main Header - Always sticky */}
      <header className="bg-white shadow-md sticky top-0 z-40">

        {/* Main Header */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            {/* Hamburger Menu Button */}
            <button
              onClick={() => setIsSidebarOpen(!isSidebarOpen)}
              className="text-gray-700 hover:text-orange-600 mr-4 cursor-pointer"
            >
              <Menu className="h-6 w-6" />
            </button>

            {/* Logo */}
            <Link href="/" className="flex items-center">
              <span className="text-2xl font-bold text-orange-600">AllRecipes</span>
            </Link>

            {/* Search Bar - Desktop */}
            <div className="hidden md:flex flex-1 max-w-2xl mx-8">
              <div className="relative w-full">
                <input
                  type="text"
                  placeholder="Find a recipe or ingredient..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full px-4 py-2 pl-10 pr-4 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                />
                <Search className="absolute left-3 top-2.5 h-5 w-5 text-gray-400" />
              </div>
            </div>

            {/* User Actions */}
            <div className="flex items-center space-x-4">
              <button className="hidden md:flex items-center space-x-1 text-gray-700 hover:text-orange-600 cursor-pointer">
                <Plus className="h-5 w-5" />
                <span>Add Recipe</span>
              </button>
              <button className="text-gray-700 hover:text-orange-600 cursor-pointer">
                <Heart className="h-6 w-6" />
              </button>
              <button className="text-gray-700 hover:text-orange-600 cursor-pointer">
                <User className="h-6 w-6" />
              </button>
              <button
                onClick={() => setIsMenuOpen(!isMenuOpen)}
                className="lg:hidden text-gray-700 hover:text-orange-600 cursor-pointer"
              >
                {isMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
              </button>
            </div>
          </div>

          {/* Navigation - Desktop - Hidden when scrolled */}
          <nav className={`hidden lg:block border-t border-gray-200 transition-all duration-300 ${isScrolled ? 'h-0 overflow-hidden py-0 border-t-0' : 'h-auto'}`}>
            <div className="flex space-x-6 xl:space-x-8 py-4">
              {navigationItems.map((item) => (
                <div key={item.name} className="relative group">
                  <Link
                    href={item.href}
                    className="text-gray-700 hover:text-orange-600 font-medium transition-colors duration-200 text-sm xl:text-base"
                  >
                    {item.name}
                  </Link>
                  {/* Dropdown Menu */}
                  <div className="absolute left-0 mt-2 w-64 bg-white shadow-lg rounded-md opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
                    <div className="py-2">
                      {item.submenu.map((subItem) => (
                        <Link
                          key={subItem.name}
                          href={subItem.href}
                          className="block px-4 py-2 text-sm text-gray-700 hover:bg-orange-50 hover:text-orange-600"
                        >
                          {subItem.name}
                        </Link>
                      ))}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </nav>
        </div>
      </header>

      {/* Left Sidebar Menu */}
      {isSidebarOpen && (
        <>
          {/* Overlay */}
          <div
            className="fixed inset-0 z-50 sidebar-overlay"
            onClick={() => setIsSidebarOpen(false)}
          />

          {/* Sidebar */}
          <div className="fixed left-0 top-0 h-full w-80 bg-white shadow-lg z-60 transform transition-transform duration-300 ease-in-out overflow-y-auto sidebar-slide-in">
            {/* Sidebar Header */}
            <div className="flex items-center justify-between p-4 border-b border-gray-200">
              <span className="text-xl font-bold text-orange-600">allrecipes</span>
              <button
                onClick={() => setIsSidebarOpen(false)}
                className="text-gray-500 hover:text-gray-700 cursor-pointer"
              >
                <X className="h-6 w-6" />
              </button>
            </div>

            {/* Search in Sidebar */}
            <div className="p-4 border-b border-gray-200">
              <h3 className="text-lg font-semibold mb-3">Search</h3>
              <div className="relative">
                <input
                  type="text"
                  placeholder="What are you looking for?"
                  className="w-full px-4 py-2 pl-4 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500"
                />
                {/* <Search className="absolute left-3 top-2.5 h-5 w-5 text-gray-400" /> */}
                <button className="absolute right-2 top-2 bg-orange-500 text-white px-1 py-1 rounded cursor-pointer">
                  <Search className="h-4 w-4" />
                </button>
              </div>
            </div>

            {/* Navigation Menu */}
            <div className="py-2">
              {navigationItems.map((item) => (
                <div key={item.name} className="border-b border-gray-100">
                  <Link
                    href={item.href}
                    className="flex items-center justify-between px-4 py-3 text-gray-700 hover:bg-orange-50 hover:text-orange-600 font-medium uppercase text-sm"
                    onClick={() => setIsSidebarOpen(false)}
                  >
                    {item.name}
                    <span className="text-gray-400">›</span>
                  </Link>
                </div>
              ))}
            </div>

            {/* Additional Links */}
            <div className="border-t border-gray-200 mt-4">
              <Link
                href="/magazine"
                className="block px-4 py-3 text-orange-600 hover:bg-orange-50 font-medium uppercase text-sm"
                onClick={() => setIsSidebarOpen(false)}
              >
                GET THE MAGAZINE
              </Link>
            </div>

            {/* Account Section */}
            <div className="border-t border-gray-200">
              <Link
                href="/login"
                className="flex items-center px-4 py-3 text-gray-700 hover:bg-orange-50 hover:text-orange-600"
                onClick={() => setIsSidebarOpen(false)}
              >
                <User className="h-5 w-5 mr-2 text-orange-500" />
                Log In
              </Link>
              <Link
                href="/magazine"
                className="block px-4 py-2 text-gray-600 hover:bg-orange-50 hover:text-orange-600 text-sm"
                onClick={() => setIsSidebarOpen(false)}
              >
                Magazine
              </Link>
              <Link
                href="/newsletter"
                className="block px-4 py-2 text-gray-600 hover:bg-orange-50 hover:text-orange-600 text-sm"
                onClick={() => setIsSidebarOpen(false)}
              >
                Newsletters
              </Link>
              <Link
                href="/sweepstakes"
                className="block px-4 py-2 text-gray-600 hover:bg-orange-50 hover:text-orange-600 text-sm"
                onClick={() => setIsSidebarOpen(false)}
              >
                Sweepstakes
              </Link>
            </div>
          </div>
        </>
      )}

      {/* Mobile Menu */}
      {isMenuOpen && (
        <div className="lg:hidden bg-white border-t border-gray-200">
          {/* Mobile Search */}
          <div className="px-4 py-3 border-b border-gray-200">
            <div className="relative">
              <input
                type="text"
                placeholder="Find a recipe..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full px-4 py-2 pl-10 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500"
              />
              <Search className="absolute left-3 top-2.5 h-5 w-5 text-gray-400" />
            </div>
          </div>

          {/* Mobile Navigation */}
          <div className="py-2">
            {navigationItems.map((item) => (
              <div key={item.name}>
                <Link
                  href={item.href}
                  className="block px-4 py-3 text-gray-700 hover:bg-orange-50 hover:text-orange-600 font-medium"
                >
                  {item.name}
                </Link>
                <div className="pl-8">
                  {item.submenu.slice(0, 4).map((subItem) => (
                    <Link
                      key={subItem.name}
                      href={subItem.href}
                      className="block px-4 py-2 text-sm text-gray-600 hover:bg-orange-50 hover:text-orange-600"
                    >
                      {subItem.name}
                    </Link>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </>
  );
};

export default Header;

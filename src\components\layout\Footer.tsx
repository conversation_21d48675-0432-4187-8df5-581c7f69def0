import React from 'react';
import Link from 'next/link';
import { Facebook, Instagram, Twitter, Youtube, Hash } from 'lucide-react';

const Footer = () => {
  const footerSections = [
    {
      title: 'Recipes',
      links: [
        { name: 'Dinner Recipes', href: '/dinners' },
        { name: 'Breakfast Recipes', href: '/breakfast' },
        { name: 'Lunch Recipes', href: '/lunch' },
        { name: 'Dessert Recipes', href: '/desserts' },
        { name: 'Appetizer Recipes', href: '/appetizers' },
        { name: 'Salad Recipes', href: '/salads' }
      ]
    },
    {
      title: 'Cuisines',
      links: [
        { name: 'Italian Recipes', href: '/cuisines/italian' },
        { name: 'Mexican Recipes', href: '/cuisines/mexican' },
        { name: 'Chinese Recipes', href: '/cuisines/chinese' },
        { name: 'Indian Recipes', href: '/cuisines/indian' },
        { name: 'French Recipes', href: '/cuisines/french' },
        { name: 'Japanese Recipes', href: '/cuisines/japanese' }
      ]
    },
    {
      title: 'Kitchen Tips',
      links: [
        { name: 'Cooking Techniques', href: '/kitchen-tips/techniques' },
        { name: 'Equipment Reviews', href: '/kitchen-tips/equipment' },
        { name: 'Ingredient Guides', href: '/kitchen-tips/ingredients' },
        { name: 'Meal Planning', href: '/kitchen-tips/meal-planning' },
        { name: 'Food Safety', href: '/kitchen-tips/food-safety' },
        { name: 'Substitutions', href: '/kitchen-tips/substitutions' }
      ]
    },
    {
      title: 'About',
      links: [
        { name: 'About Us', href: '/about' },
        { name: 'Contact Us', href: '/contact' },
        { name: 'Privacy Policy', href: '/privacy' },
        { name: 'Terms of Service', href: '/terms' },
        { name: 'Editorial Guidelines', href: '/editorial' },
        { name: 'Careers', href: '/careers' }
      ]
    }
  ];

  const socialLinks = [
    { name: 'Facebook', icon: Facebook, href: 'https://facebook.com/allrecipes' },
    { name: 'Instagram', icon: Instagram, href: 'https://instagram.com/allrecipes' },
    { name: 'Pinterest', icon: Hash, href: 'https://pinterest.com/allrecipes' },
    { name: 'Twitter', icon: Twitter, href: 'https://twitter.com/allrecipes' },
    { name: 'YouTube', icon: Youtube, href: 'https://youtube.com/allrecipes' }
  ];

  return (
    <footer className="bg-gray-900 text-white">

      {/* Main Footer Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {footerSections.map((section) => (
            <div key={section.title}>
              <h4 className="text-lg font-semibold mb-4 text-orange-400">{section.title}</h4>
              <ul className="space-y-2">
                {section.links.map((link) => (
                  <li key={link.name}>
                    <Link
                      href={link.href}
                      className="text-gray-300 hover:text-white transition-colors duration-200"
                    >
                      {link.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        {/* Social Media Links */}
        <div className="border-t border-gray-700 mt-12 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="mb-4 md:mb-0">
              <Link href="/" className="text-2xl font-bold text-orange-400">
                AllRecipes
              </Link>
              <p className="text-gray-400 mt-2">
                America&apos;s #1 Trusted Recipe Resource since 1997
              </p>
            </div>
            
            <div className="flex space-x-6">
              {socialLinks.map((social) => {
                const IconComponent = social.icon;
                return (
                  <a
                    key={social.name}
                    href={social.href}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-gray-400 hover:text-white transition-colors duration-200"
                    aria-label={social.name}
                  >
                    <IconComponent className="h-6 w-6" />
                  </a>
                );
              })}
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-gray-700 mt-8 pt-8 text-center text-gray-400 text-sm">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p>&copy; 2024 AllRecipes. All rights reserved.</p>
            <div className="flex space-x-6 mt-4 md:mt-0">
              <Link href="/privacy" className="hover:text-white transition-colors duration-200">
                Privacy Policy
              </Link>
              <Link href="/terms" className="hover:text-white transition-colors duration-200">
                Terms of Service
              </Link>
              <Link href="/cookies" className="hover:text-white transition-colors duration-200">
                Cookie Policy
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;

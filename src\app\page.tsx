import React from 'react';
import Link from 'next/link';
import { TrendingUp } from 'lucide-react';
import RecipeCard from '@/components/ui/RecipeCard';
import CategoryCard from '@/components/ui/CategoryCard';
import { featuredRecipes } from '@/data/recipes';
import { categories } from '@/data/categories';

export default function Home() {
  const heroRecipes = featuredRecipes.slice(0, 3);
  const trendingRecipes = featuredRecipes;
  const popularCategories = categories.slice(0, 6);

  return (
    <div className="bg-white">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-r from-orange-500 to-red-500 text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Find & Share Everyday
              <br />
              <span className="text-yellow-300">Cooking Inspiration</span>
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-orange-100">
              Discover thousands of recipes from home cooks like you
            </p>


          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="bg-gray-900 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
            <div>
              <div className="text-4xl font-bold text-orange-400 mb-2">51K+</div>
              <div className="text-gray-300">Original Recipes</div>
            </div>
            <div>
              <div className="text-4xl font-bold text-orange-400 mb-2">7M+</div>
              <div className="text-gray-300">Ratings & Reviews</div>
            </div>
            <div>
              <div className="text-4xl font-bold text-orange-400 mb-2">67M</div>
              <div className="text-gray-300">Home Cooks</div>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Recipes */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Featured Recipes
            </h2>
            <p className="text-xl text-gray-600">
              Hand-picked recipes that our community loves
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {heroRecipes.map((recipe) => (
              <RecipeCard key={recipe.id} recipe={recipe} />
            ))}
          </div>
        </div>
      </section>

      {/* Popular Categories */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Popular Categories
            </h2>
            <p className="text-xl text-gray-600">
              Explore recipes by your favorite meal types
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {popularCategories.map((category) => (
              <CategoryCard key={category.id} category={category} />
            ))}
          </div>

          <div className="text-center mt-8">
            <Link
              href="/categories"
              className="inline-flex items-center px-6 py-3 bg-orange-600 hover:bg-orange-700 text-white font-semibold rounded-lg transition-colors duration-200"
            >
              View All Categories
            </Link>
          </div>
        </div>
      </section>

      {/* Trending Now */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between mb-12">
            <div>
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4 flex items-center">
                <TrendingUp className="h-8 w-8 text-orange-600 mr-3" />
                Trending Now
              </h2>
              <p className="text-xl text-gray-600">
                What everyone&apos;s cooking this week
              </p>
            </div>
            <Link
              href="/trending"
              className="text-orange-600 hover:text-orange-700 font-semibold"
            >
              See All →
            </Link>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {trendingRecipes.map((recipe) => (
              <RecipeCard key={recipe.id} recipe={recipe} />
            ))}
          </div>
        </div>
      </section>

      {/* Newsletter CTA */}
      <section className="py-16 bg-orange-600">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
            Never Miss a Recipe
          </h2>
          <p className="text-xl text-orange-100 mb-8">
            Get the latest recipes, cooking tips, and kitchen inspiration delivered to your inbox.
          </p>
          <div className="max-w-md mx-auto flex border-2 border-orange-300 rounded-lg">
            <input
              type="email"
              placeholder="Enter your email"
              className="flex-1 px-4 py-3 rounded-l-lg text-gray-900 focus:outline-none focus:ring-2 focus:ring-orange-300"
            />
            <button className="bg-orange-700 hover:bg-orange-800 px-6 py-3 rounded-r-lg text-white font-semibold transition-colors duration-200">
              Subscribe
            </button>
          </div>
        </div>
      </section>
    </div>
  );
}

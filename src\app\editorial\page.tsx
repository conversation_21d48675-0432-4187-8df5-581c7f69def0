import React from 'react';
import { CheckCircle, Users, Award, BookOpen } from 'lucide-react';

export default function EditorialPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-orange-600 text-white py-16">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-4">
              Editorial Guidelines
            </h1>
            <p className="text-xl text-orange-100">
              Our commitment to quality and accuracy
            </p>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="bg-white rounded-lg shadow-lg p-8">
          <div className="prose prose-lg max-w-none">
            <section className="mb-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">Our Mission</h2>
              <p className="text-gray-700 mb-4">
                At AllRecipes, we are committed to providing our community with accurate, tested, 
                and reliable recipes that inspire confidence in the kitchen. Our editorial guidelines 
                ensure that every piece of content meets our high standards for quality and usefulness.
              </p>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">Editorial Standards</h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div className="flex items-start space-x-3">
                  <CheckCircle className="h-6 w-6 text-green-600 mt-1 flex-shrink-0" />
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-2">Recipe Testing</h3>
                    <p className="text-gray-700 text-sm">
                      All featured recipes are tested multiple times by our culinary team to ensure accuracy and reliability.
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <Users className="h-6 w-6 text-blue-600 mt-1 flex-shrink-0" />
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-2">Community Driven</h3>
                    <p className="text-gray-700 text-sm">
                      We value community feedback and incorporate user reviews and ratings into our editorial process.
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <Award className="h-6 w-6 text-yellow-600 mt-1 flex-shrink-0" />
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-2">Expert Review</h3>
                    <p className="text-gray-700 text-sm">
                      Our team of culinary professionals reviews all content for accuracy and best practices.
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <BookOpen className="h-6 w-6 text-purple-600 mt-1 flex-shrink-0" />
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-2">Clear Instructions</h3>
                    <p className="text-gray-700 text-sm">
                      We ensure all recipes have clear, step-by-step instructions that are easy to follow.
                    </p>
                  </div>
                </div>
              </div>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">Recipe Submission Guidelines</h2>
              <p className="text-gray-700 mb-4">
                When submitting recipes to AllRecipes, please ensure they meet the following criteria:
              </p>
              <ul className="list-disc pl-6 text-gray-700 space-y-2">
                <li>Include complete ingredient lists with accurate measurements</li>
                <li>Provide clear, step-by-step cooking instructions</li>
                <li>Test the recipe at least once before submission</li>
                <li>Include cooking times, serving sizes, and difficulty levels</li>
                <li>Use high-quality photos that accurately represent the finished dish</li>
                <li>Provide nutritional information when possible</li>
              </ul>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">Content Review Process</h2>
              <div className="bg-orange-50 rounded-lg p-6 mb-6">
                <h3 className="font-semibold text-gray-900 mb-3">Our 4-Step Review Process:</h3>
                <ol className="list-decimal pl-6 text-gray-700 space-y-2">
                  <li><strong>Initial Review:</strong> Content is checked for completeness and basic requirements</li>
                  <li><strong>Culinary Review:</strong> Our chefs review recipes for accuracy and feasibility</li>
                  <li><strong>Testing Phase:</strong> Selected recipes are tested in our test kitchen</li>
                  <li><strong>Final Approval:</strong> Content is approved for publication after final review</li>
                </ol>
              </div>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">Accuracy and Corrections</h2>
              <p className="text-gray-700 mb-4">
                We strive for accuracy in all our content. If you notice an error in any recipe or article, 
                please let us know immediately. We are committed to:
              </p>
              <ul className="list-disc pl-6 text-gray-700 space-y-2">
                <li>Promptly investigating reported errors</li>
                <li>Making necessary corrections quickly</li>
                <li>Clearly marking updated content</li>
                <li>Maintaining transparency about changes made</li>
              </ul>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">Nutritional Information</h2>
              <p className="text-gray-700 mb-4">
                Nutritional information provided on AllRecipes is calculated using standard food databases 
                and should be considered estimates. We recommend consulting with a healthcare professional 
                for specific dietary needs and restrictions.
              </p>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">Food Safety</h2>
              <p className="text-gray-700 mb-4">
                Food safety is our top priority. All recipes and cooking advice follow current food safety 
                guidelines from recognized authorities such as the USDA and FDA. We regularly update our 
                content to reflect the latest food safety recommendations.
              </p>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">Contact Our Editorial Team</h2>
              <p className="text-gray-700">
                Have questions about our editorial process or want to report an issue? 
                Contact our editorial <NAME_EMAIL> or through our contact form.
              </p>
            </section>
          </div>
        </div>
      </div>
    </div>
  );
}
